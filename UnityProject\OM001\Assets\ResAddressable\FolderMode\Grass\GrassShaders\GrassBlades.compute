// @Minionsart version
// credits  to  forkercat https://gist.github.com/junhaowww/fb6c030c17fe1e109a34f1c92571943f
// and  NedMakesGames https://gist.github.com/NedMakesGames/3e67fabe49e2e3363a657ef8a6a09838
// for the base setup for compute shaders

// Each #kernel tells which function to compile; you can have many kernels
#pragma kernel Main
// Define some constants
#define TWO_PI      6.28318530717958647693

// This describes a vertex on the source mesh
struct SourceVertex
{
    float3 positionWS; // position in world space
    float3 normalOS;
    float2 uv;  // contains widthMultiplier, heightMultiplier
    float3 color;
};

StructuredBuffer<SourceVertex> _SourceVertices;
StructuredBuffer<int> _VisibleIDBuffer;

StructuredBuffer<float> _CutBuffer;// added for cutting
//StructuredBuffer<float3> _OverrideColorBuffer;// added for color override


// This describes a vertex on the generated mesh
struct DrawVertex
{
    float3 positionWS; // The position in world space
    float2 uv;
};

// A triangle on the generated mesh
struct DrawTriangle
{
    float3 normalOS;  
    float3 diffuseColor;
     float4 extraBuffer;
    DrawVertex vertices[3]; // The three points on the triangle
};

// A buffer containing the generated mesh
AppendStructuredBuffer<DrawTriangle> _DrawTriangles;

// The indirect draw call args, as described in the renderer script
struct IndirectArgs
{
    uint numVerticesPerInstance;
    uint numInstances;
    uint startVertexIndex;
    uint startInstanceIndex;
    uint startLocation;
};

// The kernel will count the number of vertices, so this must be RW enabled
RWStructuredBuffer<IndirectArgs> _IndirectArgsBuffer;

// These values are bounded by limits in C# scripts,
// because in the script we need to specify the buffer size
#define GRASS_BLADES 5
#define GRASS_SEGMENTS 2// segments per blade
#define GRASS_NUM_VERTICES_PER_BLADE (GRASS_SEGMENTS * 2)+ 1

// ----------------------------------------

// Variables set by the renderer
int _NumSourceVertices;
// Time
float _Time;

// Grass
half _GrassHeight;
half _GrassWidth;
float _GrassRandomHeightMin, _GrassRandomHeightMax;

// Wind
half _WindSpeed;
float _WindStrength;

// Interactor
half _InteractorStrength;

// Blade
half _BladeRadius;
float _BladeForward;
float _BladeCurve;
float _BottomWidth;
int _MaxBladesPerVertex;
int _MaxSegmentsPerBlade;
float _MinHeight, _MinWidth;
float _MaxHeight, _MaxWidth;
// Camera
float _MinFadeDist, _MaxFadeDist;

// Uniforms
uniform float4 _PositionsMoving[5];
uniform float _InteractorsLength;
uniform float3 _CameraPositionWS;


float3x3 _LocalToWorld;

// ----------------------------------------
// Helper Functions

float rand(float3 co)
{
    return frac(
    sin(dot(co.xyz, float3(12.9898, 78.233, 53.539))) * 43758.5453);
}

float Unity_RandomRange_float(float2 Seed, float Min, float Max)
{
    float randomno =  frac(sin(dot(Seed, float2(12.9898, 78.233)))*43758.5453);
    return lerp(Min, Max, randomno);
}

// Optimized wind calculation using fast approximations
float3 CalculateOptimizedWind(float3 worldPos, float time, float windSpeed, float windStrength)
{
    // Use fast polynomial approximations instead of sin/cos
    // This reduces the computational cost significantly on mobile
    float t1 = time * windSpeed + worldPos.x;
    float t2 = time * windSpeed + worldPos.z * 2.0;
    float t3 = time * windSpeed * 0.1 + worldPos.x;
    float t4 = time * windSpeed + worldPos.x * 2.0;
    float t5 = time * windSpeed + worldPos.z;

    // Fast sine approximation using polynomial (much faster than sin/cos on mobile)
    // sin(x) ≈ x - x³/6 + x⁵/120 (for small x, normalize to [-π, π])
    t1 = fmod(t1, TWO_PI) - 3.14159;
    t2 = fmod(t2, TWO_PI) - 3.14159;
    t3 = fmod(t3, TWO_PI) - 3.14159;
    t4 = fmod(t4, TWO_PI) - 3.14159;
    t5 = fmod(t5, TWO_PI) - 3.14159;

    float sin1 = t1 - (t1*t1*t1) * 0.16667;
    float sin2 = t2 - (t2*t2*t2) * 0.16667;
    float sin3 = t3 - (t3*t3*t3) * 0.16667;
    float cos1 = 1.0 - (t4*t4) * 0.5 + (t4*t4*t4*t4) * 0.04167;
    float cos2 = 1.0 - (t5*t5) * 0.5 + (t5*t5*t5*t5) * 0.04167;

    return float3(sin1 + sin2 + sin3, 0, cos1 + cos2) * windStrength;
}

// Optimized interactor calculation - pre-compute combined displacement
float3 CalculateInteractorDisplacement(float3 worldPos, float3 normal, float cut, float grassHeight, float interactorStrength)
{
    float3 combinedDisp = 0;

    // Early exit if grass is cut too low (avoid branch divergence)
    float cutThreshold = saturate((cut + 0.5 - worldPos.y) * 10.0); // smooth step instead of hard branch

    // Limit interactor processing to reduce nested loops
    int maxInteractors = min(_InteractorsLength, 3); // Limit to 3 interactors for mobile performance

    [unroll(3)] // Force unroll for better mobile performance
    for (int p = 0; p < maxInteractors; p++)
    {
        float3 playerToVertex = worldPos - _PositionsMoving[p].xyz;
        float distanceFromSphere = length(playerToVertex);
        float3 directionFromPlayer = playerToVertex / max(distanceFromSphere, 0.001); // avoid division by zero

        // Optimized radius calculation
        float radius = 1.0 - saturate(distanceFromSphere / max(_PositionsMoving[p].w, 0.001));
        radius = radius * radius; // quadratic falloff for smoother interaction

        // Simplified displacement calculation
        float3 baseXZOffset = float3(directionFromPlayer.x, 0, directionFromPlayer.z) * distanceFromSphere;
        float3 sphereDisp = (baseXZOffset * interactorStrength) - float3(0, distanceFromSphere, 0);

        combinedDisp += sphereDisp * radius * cutThreshold;
    }

    return combinedDisp;
}

// A function to compute an rotation matrix which rotates a point
// by angle radians around the given axis
// By Keijiro Takahashi
float3x3 AngleAxis3x3(float angle, float3 axis)
{
    float c, s;
    sincos(angle, s, c);

    float t = 1 - c;
    float x = axis.x;
    float y = axis.y;
    float z = axis.z;

    return float3x3(
    t * x * x + c, t * x * y - s * z, t * x * z + s * y,
    t * x * y + s * z, t * y * y + c, t * y * z - s * x,
    t * x * z - s * y, t * y * z + s * x, t * z * z + c);
}

// Generate each grass vertex for output triangles
DrawVertex GrassVertex(float3 positionWS, float width, float height,
float offset, float curve, float2 uv, float3x3 rotation)
{
    DrawVertex output = (DrawVertex)0;
    float3 newPosOS = positionWS + mul(rotation, float3(width, height, curve + offset));
    output.positionWS = newPosOS;
    output.uv = uv;
    return output;
}

// ----------------------------------------

// The main kernel - optimized for mobile GPU (64 threads per group)
[numthreads(64, 1, 1)]
void Main(uint id : SV_DispatchThreadID)
{
    // Early exit optimization using mathematical functions instead of branches
    float validThread = step((float)id, (float)_NumSourceVertices - 1.0);

    int usableID = _VisibleIDBuffer[id];
    float validID = step(0.0, (float)usableID); // 1.0 if usableID >= 0, 0.0 if -1

    // Combined early exit condition
    float shouldProcess = validThread * validID;

    // get the right data at the visible ids (will be ignored if shouldProcess is 0)
    SourceVertex sv = _SourceVertices[max(0, usableID)]; // Avoid negative indexing
    float cut = _CutBuffer[max(0, usableID)];

    // fading on max distance
    float distanceFromCamera = distance(sv.positionWS, _CameraPositionWS);
    float distanceFade = 1.0 - saturate((distanceFromCamera - _MinFadeDist) / max(_MaxFadeDist - _MinFadeDist, 0.001));

    // Use smooth step instead of hard cutoff for better performance
    distanceFade = saturate(distanceFade);
    shouldProcess *= step(0.01, distanceFade); // Only process if fade > 0.01

    // Blades & Segments
    int numBladesPerVertex = min(GRASS_BLADES, max(1, _MaxBladesPerVertex));
    int numSegmentsPerBlade = min(GRASS_SEGMENTS, max(1, _MaxSegmentsPerBlade));;
    // -1 is because the top part of the grass only has 1 triangle
    int numTrianglesPerBlade = (numSegmentsPerBlade - 1) * 2 + 1;
    
    // normal
    float3 perpendicularAngle = float3(0, 0, 1);
    float3 faceNormal = sv.normalOS;
  
    // Use mathematical function instead of branch for color override
    float isCut = step(0.0, cut + 0.5); // 1.0 if cut != -1, 0.0 if cut == -1
    sv.color = lerp(sv.color, float3(0,1,0), isCut); // Blend to green if cut
    // Optimized Wind calculation
    float3 wind1 = CalculateOptimizedWind(sv.positionWS, _Time.x, _WindSpeed, _WindStrength);
    
    // Set grass height and width
    _GrassHeight = sv.uv.y;
    _GrassWidth = sv.uv.x;  // UV.x == width multiplier (set in GrassPainter.cs)
     float randomisedPos = rand(sv.positionWS.xyz);
     // random height offsets
    float randomOffset = Unity_RandomRange_float(sv.positionWS.xz, _GrassRandomHeightMin, _GrassRandomHeightMax);
    _GrassHeight = clamp(_GrassHeight + randomOffset, _MinHeight, _MaxHeight);
    _GrassWidth=  clamp(_GrassWidth, _MinWidth, _MaxWidth);
    _GrassWidth *= saturate(distanceFade);
    _BladeForward *= _GrassHeight;

    // Pre-calculate interactor displacement once per vertex (moved outside blade loop)
    float3 baseInteractorDisp = CalculateInteractorDisplacement(sv.positionWS, sv.normalOS, cut, _GrassHeight, _InteractorStrength);

    // LOD system: reduce complexity based on distance
    float lodFactor = saturate(distanceFade);
    int actualBladesCount = (int)(numBladesPerVertex * lodFactor * shouldProcess);
    int actualSegmentsPerBlade = max(1, (int)(numSegmentsPerBlade * lodFactor));

    // Early exit if no processing needed
    [branch]
    if (shouldProcess < 0.5 || actualBladesCount <= 0)
        return;

    for (int j = 0; j < actualBladesCount; j++)
    {
        // vertices arrays - size based on LOD
        DrawVertex drawVertices[GRASS_NUM_VERTICES_PER_BLADE];

        // set rotation and radius of the blades
        float3x3 facingRotationMatrix = AngleAxis3x3(randomisedPos * TWO_PI + j, sv.normalOS);
        float bladeRadius = j / (float) numBladesPerVertex;
        float offset = (1 - bladeRadius) * _BladeRadius;

        // Use pre-calculated interactor displacement
        float3 offsetWorldPos = sv.positionWS + mul(facingRotationMatrix, float3(0, 0, offset));
        float3 combinedDisp = baseInteractorDisp;
        // create blade with LOD-based segment count
        for (int i = 0; i < actualSegmentsPerBlade; i++)
        {
            // taper width, increase height
            float t = i / (float) actualSegmentsPerBlade;
            float segmentHeight = _GrassHeight * t;
            float segmentWidth = _GrassWidth * (1 - t);

            // Use mathematical function instead of branch for bottom width
            float isFirstSegment = 1.0 - step(0.5, (float)i);
            segmentWidth = lerp(segmentWidth, _BottomWidth * segmentWidth, isFirstSegment);

            float segmentForward = pow(abs(t), _BladeCurve) * _BladeForward;

            // Optimized position calculation with reduced branching
            float3 displacementFactor = float3(t, t, t);
            float3 baseDisplacement = (combinedDisp + wind1 + (faceNormal * _GrassHeight)) * displacementFactor;
            float3 newPos = sv.positionWS + baseDisplacement * (1.0 - isFirstSegment);

            // Append First Vertex
            drawVertices[i * 2] = GrassVertex(newPos, segmentWidth, segmentHeight, offset, segmentForward, float2(0, t), facingRotationMatrix);
            // Append Second Vertex
            drawVertices[i * 2 + 1] = GrassVertex(newPos, -segmentWidth, segmentHeight, offset, segmentForward, float2(1, t), facingRotationMatrix);
        }
        // Append Top Vertex
        float3 topPosOS = sv.positionWS + combinedDisp + wind1 + (faceNormal * _GrassHeight);
        drawVertices[actualSegmentsPerBlade * 2] = GrassVertex(topPosOS, 0, _GrassHeight, offset, _BladeForward, float2(0.5, 1), facingRotationMatrix);
          
          // add to indirect arguments buffer with the correct vertexcount       
          InterlockedAdd(_IndirectArgsBuffer[0].numVerticesPerInstance, numTrianglesPerBlade * 3);

        // add to the drawbuffer to be read by the final shader
        for (int k = 0; k < numTrianglesPerBlade; ++k)
        {
            DrawTriangle tri = (DrawTriangle)0;
            tri.normalOS = faceNormal;
            tri.diffuseColor = sv.color;
            tri.extraBuffer = float4(cut, 0,0,0);
            tri.vertices[0] = drawVertices[k];
            tri.vertices[1] = drawVertices[k + 1];
            tri.vertices[2] = drawVertices[k + 2];
            _DrawTriangles.Append(tri);
        }
    }
   
}

